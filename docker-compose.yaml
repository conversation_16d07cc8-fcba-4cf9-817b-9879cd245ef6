---
services:
  exnoapf-olympus-othrys-pf-information-mysql:
    image: mysql:8.0
    platform: linux/amd64
    container_name: exnoapf-olympus-othrys-pf-information-mysql
#    command:
#      - --sql-mode=NO_ZERO_DATE  # freegameDBに近づけるため（Auroraの場合は設定を削除する）
#      - --default-time-zone=Asia/Tokyo  # Auroraの時の設定
    environment:
#      - "TZ=Asia/Tokyo"  # JSTになります（Auroraの場合は設定を削除する）
      - MYSQL_ROOT_PASSWORD=root
    ports:
      - "3439:3306"
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      start_period: 30s
      timeout: 5s
      retries: 10
volumes:
  db-data:
    driver: local
  db-log:
    driver: local
  cache-data:
    driver: local
