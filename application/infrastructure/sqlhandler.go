package infrastructure

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"fmt"
	"time"

	"git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/interfaces"
	"git.dmm.com/exnoa-platform/exnoapf-shark-go-microkit/metrics"
	"git.dmm.com/exnoa-platform/exnoapf-shark-go-microkit/rdbkit"
	mysqlcon "git.dmm.com/exnoa-platform/exnoapf-shark-go-microkit/rdbkit/mysql"
	ddkit "git.dmm.com/exnoa-platform/exnoapf-shark-go-microkit/tracekit/datadog"
	"github.com/go-sql-driver/mysql"
)

// RDBKit infrastructureレイヤー.
type RDBKit struct {
	RDB *rdbkit.RDBKit
}

func NewRDBKit(dbConfig *DBConfig, ddAgentExist, openMetricsEnable bool) (*RDBKit, error) {
	var _ interfaces.RDBKit = (*RDBKit)(nil) // Verify Interface Compliance in compile time
	rdb := new(RDBKit)
	connRead, err := rdb.generateRDBConnectorRead(dbConfig)
	if err != nil {
		return nil, fmt.Errorf("failed infrastructure NewRDBKit() Read: %w", err)
	}
	connWrite, err := rdb.generateRDBConnectorWrite(dbConfig)
	if err != nil {
		return nil, fmt.Errorf("failed infrastructure NewRDBKit() Write: %w", err)
	}

	// 共通オプション
	rdbkitOpts := []func(k *rdbkit.RDBKit) error{
		rdbkit.WithMaxOpenConns(dbConfig.MaxOpenConns),
		rdbkit.WithMaxIdleConns(dbConfig.MaxIdleConns),
		rdbkit.WithConnMaxLifetime(time.Duration(dbConfig.ConnMaxLifetime) * time.Second),
		rdbkit.WithReadConnector(connRead),
		rdbkit.WithWriteConnector(connWrite),
	}
	if ddAgentExist {
		// Datadogを利用
		rdbkitOpts = append(rdbkitOpts, rdbkit.WithTrace(ddkit.NewRDBTraceInjector(dbConfig.RDBTraceServiceName)))
	}

	rdb.RDB, err = rdbkit.NewRDBKit(rdbkitOpts...)
	if err != nil {
		return nil, fmt.Errorf("failed infrastructure NewRDBKit(): %w", err)
	}

	if openMetricsEnable {
		// Metricsを有効化する
		if err := metrics.RDBConnectionMetrics(dbConfig.RDBMetricsName, rdb.RDB.WriteDB, rdb.RDB.ReadDB); err != nil {
			return nil, fmt.Errorf("failed infrastructure ConnectionMetrics(): %w", err)
		}
	}

	return rdb, nil
}

func (r *RDBKit) generateRDBConnectorRead(dbConfig *DBConfig) (driver.Connector, error) {
	return mysqlcon.NewConnector( //nolint:wrapcheck
		mysqlcon.WithDatabaseName(dbConfig.Database),
		mysqlcon.WithDatabaseHost(dbConfig.ReadDBHost, dbConfig.ReadDBPort),
		mysqlcon.WithDatabaseUser(dbConfig.ReadDBUser),
		mysqlcon.WithDatabasePass(dbConfig.ReadDBPass),
		mysqlcon.WithDatabaseTimezone(dbConfig.DBTimezone), // 接続先のDBのタイムゾーンを確認して設定
		mysql.Charset(dbConfig.Charset, dbConfig.Collation),
	)
}

func (r *RDBKit) generateRDBConnectorWrite(dbConfig *DBConfig) (driver.Connector, error) {
	return mysqlcon.NewConnector( //nolint:wrapcheck
		mysqlcon.WithDatabaseName(dbConfig.Database),
		mysqlcon.WithDatabaseHost(dbConfig.WriteDBHost, dbConfig.WriteDBPort),
		mysqlcon.WithDatabaseUser(dbConfig.WriteDBUser),
		mysqlcon.WithDatabasePass(dbConfig.WriteDBPass),
		mysqlcon.WithDatabaseTimezone(dbConfig.DBTimezone), // 接続先のDBのタイムゾーンを確認して設定
		mysql.Charset(dbConfig.Charset, dbConfig.Collation),
	)
}

// Query RDBから一行以上をSelectする.
func (r *RDBKit) Query(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error) {
	return r.RDB.Query(ctx, query, args...) //nolint:wrapcheck
}

// QueryRow RDBから一行をSelectする.
func (r *RDBKit) QueryRow(ctx context.Context, query string, args ...interface{}) *sql.Row {
	return r.RDB.QueryRow(ctx, query, args...)
}

// WithTransaction トランザクションをかけて複数のQueryを実行する.
func (r *RDBKit) WithTransaction(ctx context.Context, fn func(*sql.Tx) error) error {
	return r.RDB.WithTransaction(ctx, fn) //nolint:wrapcheck
}

// Exec トランザクションをかけて一行のQueryを実行する.
func (r *RDBKit) Exec(ctx context.Context, query string, args ...interface{}) (sql.Result, error) {
	return r.RDB.Exec(ctx, query, args...) //nolint:wrapcheck
}

// Close DB接続を終了する.
func (r *RDBKit) Close() {
	r.RDB.Close()
}

// Ping DB接続を確かめる.
func (r *RDBKit) Ping() error {
	return r.RDB.Ping() //nolint:wrapcheck
}
