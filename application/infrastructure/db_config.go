package infrastructure

import (
	"fmt"

	"github.com/kelseyhightower/envconfig"
)

// DBConfig データベース接続関連情報を持つストラクチャー.
type DBConfig struct {
	Database            string
	Charset             string
	Collation           string
	DBTimezone          string
	WriteDBHost         string
	WriteDBPort         int
	WriteDBUser         string
	WriteDBPass         string
	ReadDBHost          string
	ReadDBPort          int
	ReadDBUser          string
	ReadDBPass          string
	MaxOpenConns        int
	MaxIdleConns        int
	ConnMaxLifetime     int
	RDBTraceServiceName string
	RDBMetricsName      string
}

// FreeGameDeveloperDBConfig データベース接続関連情報を持つストラクチャー.
type FreeGameDeveloperDBConfig struct {
	Database   string `envconfig:"DB_FREEGAME_DEVELOPER_DATABASE"`
	Charset    string `default:"utf8" envconfig:"DB_FREEGAME_DEVELOPER_CHARSET"`
	Collation  string `default:"utf8_general_ci" envconfig:"DB_FREEGAME_DEVELOPER_COLLATION"`
	DBTimezone string `required:"true" envconfig:"DB_FREEGAME_DEVELOPER_TIMEZONE"`

	WriteDBHost string `required:"true" envconfig:"DB_FREEGAME_DEVELOPER_HOST"`
	WriteDBPort int    `required:"true" envconfig:"DB_FREEGAME_DEVELOPER_PORT"`
	WriteDBUser string `required:"true" envconfig:"DB_FREEGAME_DEVELOPER_USERNAME"`
	WriteDBPass string `required:"true" envconfig:"DB_FREEGAME_DEVELOPER_PASSWORD"`

	ReadDBHost string `required:"true" envconfig:"DB_FREEGAME_DEVELOPER_SLAVE_HOST"`
	ReadDBPort int    `required:"true" envconfig:"DB_FREEGAME_DEVELOPER_SLAVE_PORT"`
	ReadDBUser string `required:"true" envconfig:"DB_FREEGAME_DEVELOPER_SLAVE_USERNAME"`
	ReadDBPass string `required:"true" envconfig:"DB_FREEGAME_DEVELOPER_SLAVE_PASSWORD"`

	MaxOpenConns    int `required:"true" envconfig:"DB_FREEGAME_DEVELOPER_MAX_OPEN_CONNS_NUM"`
	MaxIdleConns    int `required:"true" envconfig:"DB_FREEGAME_DEVELOPER_MAX_IDLE_CONNS_NUM"`
	ConnMaxLifetime int `required:"true" envconfig:"DB_FREEGAME_DEVELOPER_CONN_MAX_LIFETIME_SEC"`

	RDBTraceServiceName string `required:"true" envconfig:"RDB_TRACE_SERVICE_NAME"`
	RDBMetricsName      string `default:"undefined" envconfig:"DB_FREEGAME_DEVELOPER_METRICS_NAME"`
}

func NewFreeGameDeveloperDBConfig() (*DBConfig, error) {
	var env FreeGameDeveloperDBConfig
	if err := envconfig.Process("", &env); err != nil {
		return nil, fmt.Errorf("failed infrastructure NewFreeGameDeveloperDBConfig: %w", err)
	}
	dbConfig := DBConfig(env) // 型を統一しておく

	return &dbConfig, nil
}
