package infrastructure

import (
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	announcementInfrastructure "git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/announcement/infrastructure"
	announcementInterfaces "git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/announcement/interfaces"
	announcementUsecases "git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/announcement/usecases"
	incidentsInfrastructure "git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/incidents/infrastructure"
	incidentsInterfaces "git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/incidents/interfaces"
	incidentsUsecases "git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/incidents/usecases"
	"git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/interfaces"
	maintenanceInfrastructure "git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/maintenance/infrastructure"
	maintenanceInterfaces "git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/maintenance/interfaces"
	maintenanceUsecases "git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/maintenance/usecases"
	"git.dmm.com/exnoa-platform/exnoapf-shark-go-microkit/ctxlog/log"
	"git.dmm.com/exnoa-platform/exnoapf-shark-go-microkit/httpkit"
	ddkit "git.dmm.com/exnoa-platform/exnoapf-shark-go-microkit/tracekit/datadog"
	"github.com/kelseyhightower/envconfig"
	echo "github.com/labstack/echo/v4"
)

type serverConfig struct {
	ServiceName      string  `required:"true" envconfig:"SERVICE_NAME"`
	TraceSampleRate  float64 `required:"true" envconfig:"TRACE_SAMPLE_RATE"`
	DDAgentExist     bool    `required:"true" envconfig:"DD_AGENT_EXIST"`
	Port             int     `default:"8080" envconfig:"PORT"`
	IsPublicEndpoint bool    `default:"false" envconfig:"IS_PUBLIC_ENDPOINT"`
}

func newServerConfig() (*serverConfig, error) {
	var env serverConfig
	if err := envconfig.Process("", &env); err != nil {
		return nil, fmt.Errorf("failed infrastructure newServerConfig: %w", err)
	}

	return &env, nil
}

// Dispatch is handle routing.
func Dispatch(freegamedeveloperDBKit interfaces.RDBKit) {
	// 各種設定値を環境変数から取得
	sc, err := newServerConfig()
	if err != nil {
		panic(err)
	}

	// Echo
	httpKitOpts := []func(*httpkit.HTTPKit) error{
		httpkit.WithHTTPLogger(log.Logger),
	}
	if sc.DDAgentExist {
		httpKitOpts = append(httpKitOpts, httpkit.WithTrace(ddkit.NewEchoServerTraceInjector(sc.ServiceName)))
	}
	httpKit, err := httpkit.NewHTTPKit(sc.Port, httpKitOpts...)
	if err != nil {
		panic(err)
	}

	// mount LivenessProbe, ReadinessProbe
	httpKit.MountLivenessProbe(LivenessProbe)
	httpKit.MountReadinessProbe(ReadinessProbe)

	// コントローラー生成
	announcementRepo := announcementInfrastructure.NewAnnouncementRepository(freegamedeveloperDBKit)
	announcementUsecase := announcementUsecases.NewAnnouncementUsecase(announcementRepo)
	announcementController := announcementInterfaces.NewAnnouncementController(announcementUsecase)
	incidentsRepo := incidentsInfrastructure.NewIncidentsRepository(freegamedeveloperDBKit)
	incidentsUsecase := incidentsUsecases.NewIncidentsUsecase(incidentsRepo)
	incidentsController := incidentsInterfaces.NewIncidentsController(incidentsUsecase)
	maintenanceRepo := maintenanceInfrastructure.NewMaintenanceRepository(freegamedeveloperDBKit)
	maintenanceUsecase := maintenanceUsecases.NewMaintenanceUsecase(maintenanceRepo)
	maintenanceController := maintenanceInterfaces.NewMaintenanceController(maintenanceUsecase)

	// ルーティング
	httpKit.Echo.GET("/announcement/list", announcementController.List)
	httpKit.Echo.GET("/announcement/detail/:id", announcementController.Detail)
	httpKit.Echo.GET("/incidents/list", incidentsController.List)
	httpKit.Echo.GET("/incidents/detail/:id", incidentsController.Detail)
	httpKit.Echo.GET("/maintenance/list", maintenanceController.List)
	httpKit.Echo.GET("/maintenance/detail/:id", maintenanceController.Detail)

	// start server
	errors := make(chan error, 1)
	go func() {
		log.Info().Msg("start")
		if err := httpKit.Start(); err != nil {
			errors <- err
		}
	}()

	// wait signal or error
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt, syscall.SIGTERM)
	select {
	case s := <-quit:
		log.Info().With("signal", s.String()).Msg("quit")
	case err := <-errors:
		panic(err)
	}
	signal.Stop(quit)
	err = httpKit.Stop()
	if err != nil {
		panic(err)
	}
}

// LivenessProbe LivenessProbe.
func LivenessProbe(c echo.Context) error {
	return c.NoContent(http.StatusOK) //nolint:wrapcheck
}

// ReadinessProbe ReadinessProbe.
func ReadinessProbe(c echo.Context) error {
	// do something application specific check
	return c.NoContent(http.StatusOK) //nolint:wrapcheck
}
