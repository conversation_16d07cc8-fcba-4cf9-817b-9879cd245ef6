package maintenanceinterfaces

import (
	"net/http"
	"strconv"
	"time"

	maintenanceUsecases "git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/maintenance/usecases"
	"github.com/labstack/echo/v4"
)

type MaintenanceController struct {
	usecase *maintenanceUsecases.MaintenanceUsecase
}

func NewMaintenanceController(u *maintenanceUsecases.MaintenanceUsecase) *MaintenanceController {
	return &MaintenanceController{usecase: u}
}

func (c *MaintenanceController) List(ctx echo.Context) error {
	// クエリパラメータの取得
	keyword := ctx.QueryParam("keyword")
	startDateStr := ctx.QueryParam("startDate")
	endDateStr := ctx.QueryParam("endDate")

	var startDate *time.Time
	if startDateStr != "" {
		parsedDate, err := time.Parse("2006-01-02", startDateStr) // YYYY-MM-DD形式をパース
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "invalid startDate format. Use YYYY-MM-DD.").WithInternal(err)
		}
		startDate = &parsedDate
	}

	var endDate *time.Time
	if endDateStr != "" {
		parsedDate, err := time.Parse("2006-01-02", endDateStr) // YYYY-MM-DD形式をパース
		if err != nil {
			return echo.NewHTTPError(http.StatusBadRequest, "invalid endDate format. Use YYYY-MM-DD.").WithInternal(err)
		}
		endDate = &parsedDate
	}

	items, err := c.usecase.List(ctx.Request().Context(), keyword, startDate, endDate)
	if err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, err.Error()).WithInternal(err)
	}

	if err := ctx.JSON(http.StatusOK, items); err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "failed to return JSON response").WithInternal(err)
	}

	return nil
}

func (c *MaintenanceController) Detail(ctx echo.Context) error {
	id, err := strconv.Atoi(ctx.Param("id"))
	if err != nil {
		return echo.NewHTTPError(http.StatusBadRequest, "invalid id").WithInternal(err)
	}
	item, err := c.usecase.GetByID(ctx.Request().Context(), id)
	if err != nil {
		return echo.NewHTTPError(http.StatusNotFound, "not found").WithInternal(err)
	}

	if err := ctx.JSON(http.StatusOK, item); err != nil {
		return echo.NewHTTPError(http.StatusInternalServerError, "failed to return JSON response").WithInternal(err)
	}

	return nil
}
