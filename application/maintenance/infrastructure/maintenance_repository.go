package maintenanceinfrastructure

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/entities"
	"git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/interfaces"
	"git.dmm.com/exnoa-platform/exnoapf-shark-go-microkit/ctxlog/log"
)

type MaintenanceRepository struct {
	db interfaces.RDBKit
}

func NewMaintenanceRepository(db interfaces.RDBKit) *MaintenanceRepository {
	return &MaintenanceRepository{db: db}
}

// メンテナンス情報の一覧を取得 or 検索条件（キーワード、日付）
func (r *MaintenanceRepository) List(ctx context.Context, keyword string, startDate, endDate *time.Time) ([]entities.MaintenanceInfo, error) {
	query := `
        SELECT id, title, description, notice_date, notice_start_date, notice_end_date, is_delete, stamp
        FROM maintenance_information
        WHERE is_delete = 0`

	var args []interface{}
	whereClauses := []string{}

	// 日付フィルタリングとキーワード検索の条件を構築
	args, whereClauses = r.buildSearchConditions(keyword, startDate, endDate, args, whereClauses)

	if len(whereClauses) > 0 {
		query += " AND " + strings.Join(whereClauses, " AND ")
	}

	query += " ORDER BY notice_date DESC"

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query maintenance information: %w", err)
	}
	defer func() {
		if cerr := rows.Close(); cerr != nil {
			log.Error().WithError(cerr).Msg("Error closing rows")
		}
		if rerr := rows.Err(); rerr != nil {
			log.Error().WithError(rerr).Msg("Error during rows iteration")
		}
	}()

	var result []entities.MaintenanceInfo
	for rows.Next() {
		var e entities.MaintenanceInfo
		if err := rows.Scan(
			&e.ID,
			&e.Title,
			&e.Description,
			&e.NoticeDate,
			&e.NoticeStartDate,
			&e.NoticeEndDate,
			&e.IsDelete,
			&e.Stamp,
		); err != nil {
			return nil, fmt.Errorf("failed to scan maintenance information row: %w", err)
		}
		result = append(result, e)
	}

	return result, nil
}

// キーワードと日付 WHERE句
func (r *MaintenanceRepository) buildSearchConditions(keyword string, startDate, endDate *time.Time, args []interface{}, whereClauses []string) ([]interface{}, []string) {
	// キーワード検索
	if keyword != "" {
		whereClauses = append(whereClauses, "(title LIKE ? OR description LIKE ?)")
		args = append(args, "%"+keyword+"%", "%"+keyword+"%")
	}

	// 日付フィルタリング
	switch {
	case startDate != nil && endDate != nil:
		// startDateとendDateの両方が指定された場合、notice_start_dateがその期間内にあるかを検索
		whereClauses = append(whereClauses, "notice_start_date BETWEEN ? AND ?")
		args = append(args, *startDate, *endDate)
	case startDate != nil:
		// startDateのみが指定された場合
		whereClauses = append(whereClauses, "notice_start_date >= ?")
		args = append(args, *startDate)
	case endDate != nil:
		// endDateのみが指定された場合
		whereClauses = append(whereClauses, "notice_start_date <= ?")
		args = append(args, *endDate)
	}

	return args, whereClauses
}

// 指定されたIDでメンテナンス情報の詳細を取得
func (r *MaintenanceRepository) GetByID(ctx context.Context, id int) (*entities.MaintenanceInfo, error) {
	row := r.db.QueryRow(ctx, `
        SELECT id, title, description, notice_date, notice_start_date, notice_end_date, is_delete, stamp
        FROM maintenance_information
        WHERE id = ? AND is_delete = 0`, id) // is_delete が 0 のレコードのみ取得

	var e entities.MaintenanceInfo
	if err := row.Scan(
		&e.ID,
		&e.Title,
		&e.Description,
		&e.NoticeDate,
		&e.NoticeStartDate,
		&e.NoticeEndDate,
		&e.IsDelete,
		&e.Stamp,
	); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("maintenance with ID %d not found: %w", id, err)
		}

		return nil, fmt.Errorf("failed to scan maintenance by ID %d: %w", id, err)
	}

	return &e, nil
}
