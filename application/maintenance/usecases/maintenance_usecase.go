package maintenanceusecases

import (
	"context"
	"fmt"
	"time"

	"git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/entities"
)

// MaintenanceRepositoryのインターフェース
type MaintenanceRepository interface {
	List(ctx context.Context, keyword string, startDate, endDate *time.Time) ([]entities.MaintenanceInfo, error)
	GetByID(ctx context.Context, id int) (*entities.MaintenanceInfo, error)
}

type MaintenanceUsecase struct {
	repo MaintenanceRepository
}

func NewMaintenanceUsecase(repo MaintenanceRepository) *MaintenanceUsecase {
	return &MaintenanceUsecase{repo: repo}
}

func (u *MaintenanceUsecase) List(ctx context.Context, keyword string, startDate, endDate *time.Time) ([]entities.MaintenanceInfo, error) {
	items, err := u.repo.List(ctx, keyword, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to list maintenance from repository: %w", err)
	}

	return items, nil
}

func (u *MaintenanceUsecase) GetByID(ctx context.Context, id int) (*entities.MaintenanceInfo, error) {
	item, err := u.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get maintenance by ID %d from repository: %w", id, err)
	}

	return item, nil
}
