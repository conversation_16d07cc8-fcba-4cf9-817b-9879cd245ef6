package announcementusecases

import (
	"context"
	"fmt"
	"time"

	"git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/entities"
)

// AnnouncementRepositoryのインターフェース
type AnnouncementRepository interface {
	List(ctx context.Context, keyword string, startDate, endDate *time.Time) ([]entities.Information, error)
	GetByID(ctx context.Context, id int) (*entities.Information, error)
}

type AnnouncementUsecase struct {
	repo AnnouncementRepository
}

func NewAnnouncementUsecase(repo AnnouncementRepository) *AnnouncementUsecase {
	return &AnnouncementUsecase{repo: repo}
}

func (u *AnnouncementUsecase) List(ctx context.Context, keyword string, startDate, endDate *time.Time) ([]entities.Information, error) {
	items, err := u.repo.List(ctx, keyword, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to list announcements from repository: %w", err)
	}

	return items, nil
}

func (u *AnnouncementUsecase) GetByID(ctx context.Context, id int) (*entities.Information, error) {
	item, err := u.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get announcement by ID %d from repository: %w", id, err)
	}

	return item, nil
}
