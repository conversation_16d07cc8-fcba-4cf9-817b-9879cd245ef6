package interfaces

import (
	"context"
	"database/sql"
)

// RDBKit インターフェース層.
type RDBKit interface {
	Query(ctx context.Context, query string, args ...interface{}) (*sql.Rows, error)
	QueryRow(ctx context.Context, query string, args ...interface{}) *sql.Row
	WithTransaction(ctx context.Context, fn func(*sql.Tx) error) error
	Exec(ctx context.Context, query string, args ...interface{}) (sql.Result, error)
	Close()
	Ping() error
}
