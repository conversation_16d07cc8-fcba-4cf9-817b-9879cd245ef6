package incidentsusecases

import (
	"context"
	"fmt"
	"time"

	"git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/entities"
)

// IncidentsRepositoryのインターフェース
type IncidentsRepository interface {
	List(ctx context.Context, keyword string, startDate, endDate *time.Time) ([]entities.Information, error)
	GetByID(ctx context.Context, id int) (*entities.Information, error)
}

type IncidentsUsecase struct {
	repo IncidentsRepository
}

func NewIncidentsUsecase(repo IncidentsRepository) *IncidentsUsecase {
	return &IncidentsUsecase{repo: repo}
}

func (u *IncidentsUsecase) List(ctx context.Context, keyword string, startDate, endDate *time.Time) ([]entities.Information, error) {
	items, err := u.repo.List(ctx, keyword, startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("failed to list incidents from repository: %w", err)
	}

	return items, nil
}

func (u *IncidentsUsecase) GetByID(ctx context.Context, id int) (*entities.Information, error) {
	item, err := u.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get incidents by ID %d from repository: %w", id, err)
	}

	return item, nil
}
