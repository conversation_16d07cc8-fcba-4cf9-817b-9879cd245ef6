package incidentsinfrastructure

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"strings"
	"time"

	"git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/entities"
	"git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/interfaces"
	"git.dmm.com/exnoa-platform/exnoapf-shark-go-microkit/ctxlog/log"
)

type IncidentsRepository struct {
	db interfaces.RDBKit
}

func NewIncidentsRepository(db interfaces.RDBKit) *IncidentsRepository {
	return &IncidentsRepository{db: db}
}

// 障害情報の一覧を取得 or 検索条件（キーワード、日付）
func (r *IncidentsRepository) List(ctx context.Context, keyword string, startDate, endDate *time.Time) ([]entities.Information, error) {
	query := `
        SELECT id, title, category, description, notice_date
        FROM information`

	var args []interface{}
	whereClauses := []string{}

	args, whereClauses = r.buildSearchConditions(keyword, startDate, endDate, args, whereClauses)

	// カテゴリ
	categories := []string{
		entities.CategoryFailureRecovery,
		entities.CategoryFailureOccurrence,
	}
	args, whereClauses = r.addCategoryFilter(categories, args, whereClauses)

	if len(whereClauses) > 0 {
		query += " WHERE " + strings.Join(whereClauses, " AND ")
	}

	query += " ORDER BY notice_date DESC"

	rows, err := r.db.Query(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to query incidents information: %w", err)
	}
	defer func() {
		if cerr := rows.Close(); cerr != nil {
			log.Error().WithError(cerr).Msg("Error closing rows")
		}
		if rerr := rows.Err(); rerr != nil {
			log.Error().WithError(rerr).Msg("Error during rows iteration")
		}
	}()

	var result []entities.Information
	for rows.Next() {
		var e entities.Information
		if err := rows.Scan(
			&e.ID,
			&e.Title,
			&e.Category,
			&e.Description,
			&e.NoticeDate,
		); err != nil {
			return nil, fmt.Errorf("failed to scan incidents information row: %w", err)
		}
		result = append(result, e)
	}

	return result, nil
}

// キーワードと日付 WHERE句
func (r *IncidentsRepository) buildSearchConditions(keyword string, startDate, endDate *time.Time, args []interface{}, whereClauses []string) ([]interface{}, []string) {
	// キーワード検索
	if keyword != "" {
		whereClauses = append(whereClauses, "(title LIKE ? OR description LIKE ?)")
		args = append(args, "%"+keyword+"%", "%"+keyword+"%")
	}

	// 日付フィルタリング
	if startDate != nil {
		whereClauses = append(whereClauses, "notice_date >= ?")
		args = append(args, *startDate)
	}

	if endDate != nil {
		endOfDay := endDate.Add(entities.OneDayInHours * time.Hour).Format("2006-01-02")
		whereClauses = append(whereClauses, "notice_date < ?")
		args = append(args, endOfDay)
	}

	return args, whereClauses
}

// カテゴリ IN句
func (r *IncidentsRepository) addCategoryFilter(categories []string, args []interface{}, whereClauses []string) ([]interface{}, []string) {
	if len(categories) > 0 {
		placeholders := make([]string, len(categories))
		for i := range categories {
			placeholders[i] = "?"
		}
		whereClauses = append(whereClauses, fmt.Sprintf("category IN (%s)", strings.Join(placeholders, ",")))
		for _, cat := range categories {
			args = append(args, cat)
		}
	}

	return args, whereClauses
}

// 指定されたIDで障害情報の詳細を取得
func (r *IncidentsRepository) GetByID(ctx context.Context, id int) (*entities.Information, error) { // メソッドレシーバを構造体名に合わせて変更
	row := r.db.QueryRow(ctx, `
        SELECT id, title, category, description, notice_date
        FROM information
        WHERE id = ?`, id)

	var e entities.Information
	if err := row.Scan(
		&e.ID,
		&e.Title,
		&e.Category,
		&e.Description,
		&e.NoticeDate,
	); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, fmt.Errorf("incidents with ID %d not found: %w", id, err)
		}

		return nil, fmt.Errorf("failed to scan incidents by ID %d: %w", id, err)
	}

	return &e, nil
}
