package main

import (
	"fmt"
	"time"

	"git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/infrastructure"
	"git.dmm.com/exnoa-platform/exnoapf-shark-go-microkit/ctxlog"
	"git.dmm.com/exnoa-platform/exnoapf-shark-go-microkit/ctxlog/log"
	"git.dmm.com/exnoa-platform/exnoapf-shark-go-microkit/metrics"
	"github.com/go-sql-driver/mysql"
	"github.com/kelseyhightower/envconfig"
	sqltrace "gopkg.in/DataDog/dd-trace-go.v1/contrib/database/sql"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
	"gopkg.in/DataDog/dd-trace-go.v1/profiler"
)

type serviceConfig struct {
	AppLogLevel       string `required:"true" envconfig:"APP_LOG_LEVEL"`
	DDAgentExist      bool   `required:"true" envconfig:"DD_AGENT_EXIST"`
	DDProfileEnable   bool   `default:"false" envconfig:"DD_PROFILE_ENABLE"`
	OpenMetricsEnable bool   `default:"false" envconfig:"OPENMETRICS_ENABLE"`
	OpenMetricsPort   int    `default:"9530" envconfig:"OPENMETRICS_PORT"`
}

func main() {
	// サーバーによらずUTCを設定（UTC以外をあえて設定したい場合は変更する）
	time.Local = time.FixedZone("UTC", 0)

	// サーバー環境変数取得
	sc, err := newServiceConfig()
	if err != nil {
		panic(err)
	}

	// ログレベル設定
	log.SetLevel(ctxlog.ToLogLevel(sc.AppLogLevel))

	// datadog の exporter モジュール(tracer）の初期化
	if sc.DDAgentExist {
		tracer.Start()
		defer tracer.Stop()
		sqltrace.Register("mysql", &mysql.MySQLDriver{}) // DBを利用するなら必要
		if sc.DDProfileEnable {
			// DatadogのProfilerを有効にする
			log.Info().Msg("Activate Datadog Profile")
			err := profiler.Start()
			if err != nil {
				log.Error().WithError(err).Msg("failed main Datadog profiler.Start()")
			}
			defer profiler.Stop()
		}
	}

	// freegamedeveloperDB
	freegamedeveloperRDBKit := newFreegameDBKit(sc)
	defer freegamedeveloperRDBKit.Close()

	// Metricsを公開
	if sc.OpenMetricsEnable {
		metrics.Expose("/metrics", sc.OpenMetricsPort)
		log.Info().Msg("metrics expose")
	}

	infrastructure.Dispatch(freegamedeveloperRDBKit)
}

func newServiceConfig() (*serviceConfig, error) {
	var env serviceConfig
	if err := envconfig.Process("", &env); err != nil {
		return nil, fmt.Errorf("failed main newServiceConfig(): %w", err)
	}

	return &env, nil
}

func newFreegameDBKit(sc *serviceConfig) *infrastructure.RDBKit {
	freegamedeveloperDBConfig, err := infrastructure.NewFreeGameDeveloperDBConfig()
	if err != nil {
		log.ErrorFatal().WithError(err).Msg("database configuration must be provided")
		panic(err)
	}

	freegamedeveloperRDBKit, err := infrastructure.NewRDBKit(freegamedeveloperDBConfig, sc.DDAgentExist, sc.OpenMetricsEnable)
	if err != nil {
		log.ErrorFatal().WithError(err).Msg("failed NewRDBKit()")
		panic(err)
	}

	return freegamedeveloperRDBKit
}
