package entities

// 共通のJSONレスポンス構造体
type Response struct {
	Status  int    `json:"status"`
	Message string `json:"message"`
	Error   any    `json:"error"`
	Body    any    `json:"body"`
}

// 成功時のJSONレスポンス構造体
type SuccessResponse struct {
	Response
}

// エラー時のJSONレスポンス構造体
type ErrorResponse struct {
	Response
}

// 共通エラー構造体
type BaseError struct {
	ErrorCode string `json:"errorCode"`
	Message   string `json:"message"`
}

// 400 Bad Request用の構造体
type BadRequestError struct {
	BaseError
	ValidationError any `json:"validationError,omitempty"`
}

// 404 Not Found用の構造体
type NotFoundError struct {
	BaseError
}

// 500 Internal Server Error用の構造体
type InternalServerError struct {
	BaseError
	Message string `json:"message"`
}
