package entities

import "time"

type MaintenanceInfo struct {
	ID              int       `db:"id" json:"id"`
	Title           string    `db:"title" json:"title"`
	Description     string    `db:"description" json:"description"`
	NoticeDate      time.Time `db:"notice_date" json:"notice_date"`
	NoticeStartDate time.Time `db:"notice_start_date" json:"notice_start_date"`
	NoticeEndDate   time.Time `db:"notice_end_date" json:"notice_end_date"`
	IsDelete        uint8     `db:"is_delete" json:"is_delete"`
	Stamp           time.Time `db:"stamp" json:"stamp"`
}
