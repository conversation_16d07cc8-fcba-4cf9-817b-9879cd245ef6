package entities

import "time"

type Information struct {
	ID          int       `db:"id" json:"id"`
	Title       string    `db:"title" json:"title"`
	Category    string    `db:"category" json:"category"`
	Description string    `db:"description" json:"description"`
	NoticeDate  time.Time `db:"notice_date" json:"notice_date"`
}

const (
	// お知らせ関連
	CategoryInfo              = "info"               // お知らせ
	CategoryInfoImportant     = "info_important"     // お知らせ_重要
	CategoryRequiredInfo      = "required_info"      // 要対応_お知らせ
	CategoryRequiredImportant = "required_important" // 要対応_重要

	// 障害関連
	CategoryFailureRecovery   = "failure_recovery"   // 障害復旧
	CategoryFailureOccurrence = "failure_occurrence" // 障害発生
)

const (
	OneDayInHours = 24
)
