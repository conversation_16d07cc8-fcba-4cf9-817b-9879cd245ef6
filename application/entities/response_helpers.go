package entities

import "net/http"

// NewSuccessResponse creates a new SuccessResponse with common OK defaults
func NewSuccessResponse(body interface{}) SuccessResponse {
	return SuccessResponse{
		Response: Response{
			Status:  http.StatusOK,
			Message: "OK",
			Error:   map[string]interface{}{},
			Body:    body,
		},
	}
}

// NewBadRequestResponse creates a new ErrorResponse for Bad Request errors
func NewBadRequestResponse(message string, validationError interface{}) ErrorResponse {
	return ErrorResponse{
		Response: Response{
			Status:  http.StatusBadRequest,
			Message: "Bad Request",
			Error: BadRequestError{
				BaseError: BaseError{
					ErrorCode: "400",
					Message:   message,
				},
				ValidationError: validationError,
			},
			Body: map[string]interface{}{},
		},
	}
}

// NewNotFoundResponse creates a new ErrorResponse for Not Found errors
func NewNotFoundResponse(message string) ErrorResponse {
	return ErrorResponse{
		Response: Response{
			Status:  http.StatusNotFound,
			Message: "Not Found",
			Error: NotFoundError{
				BaseError: BaseError{
					ErrorCode: "404",
					Message:   message,
				},
			},
			Body: map[string]interface{}{},
		},
	}
}

// NewInternalServerErrorResponse creates a new ErrorResponse for Internal Server errors
func NewInternalServerErrorResponse(message string) ErrorResponse {
	return ErrorResponse{
		Response: Response{
			Status:  http.StatusInternalServerError,
			Message: "Internal Server Error",
			Error: InternalServerError{
				BaseError: BaseError{
					ErrorCode: "500",
					Message:   message,
				},
			},
			Body: map[string]interface{}{},
		},
	}
}
