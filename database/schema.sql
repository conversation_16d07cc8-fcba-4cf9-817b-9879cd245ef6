CREATE DATABASE freegame_developer;
use freegame_developer;


CREATE TABLE `freegame_developer`.`information` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `category` varchar(20) NOT NULL DEFAULT 'info',
    `title` mediumtext,
    `description` mediumtext,
    `notice_date` date DEFAULT NULL,
    `public_status` tinyint(1) unsigned NOT NULL DEFAULT '0',
    `stamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `freegame_developer`.`maintenance_information` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'メンテナンスID',
    `title` mediumtext COMMENT 'タイトル',
    `description` mediumtext COMMENT '本文',
    `notice_date` date DEFAULT NULL COMMENT '告知日',
    `notice_start_date` date DEFAULT NULL COMMENT '告知開始日',
    `notice_end_date` date DEFAULT NULL COMMENT '告知終了日',
    `is_delete` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '削除フラグ, 0: 未除済, 1: 削除済',
    `stamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_date_and_status` (`notice_start_date`, `notice_end_date`,`is_delete`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='メンテナンス情報';

INSERT INTO `information` (`id`, `category`, `title`, `description`, `notice_date`, `public_status`, `stamp`)
    VALUES
    (1, 'info', 'お知らせタイトル1', 'お知らせ本文1', '2025-04-07', 1, '2025-04-07 12:00:00'),
    (2, 'info', 'お知らせタイトル2', 'お知らせ本文2', '2025-04-07', 1, '2025-04-07 00:00:00'),
    (3, 'info', 'お知らせタイトル3', 'お知らせ本文3', '2025-04-07', 1, '2025-04-07 00:00:00'),
    (4, 'info', 'お知らせタイトル4', 'お知らせ本文4', '2025-04-07', 1, '2025-04-07 00:00:00'),
    (5, 'info', 'お知らせタイトル5', 'お知らせ本文5', '2025-04-07', 1, '2025-04-07 00:00:00'),
    (6, 'info', 'お知らせタイトル6', 'お知らせ本文6', '2025-04-07', 1, '2025-04-07 00:00:00'),
    (7, 'info', 'お知らせタイトル7', 'お知らせ本文7', '2025-04-07', 1, '2025-04-07 00:00:00'),
    (8, 'info', 'お知らせタイトル8', 'お知らせ本文8', '2025-04-07', 1, '2025-04-07 00:00:00'),
    (9, 'info', 'お知らせタイトル9', 'お知らせ本文9', '2025-04-07', 1, '2025-04-07 00:00:00'),
    (10, 'info', 'お知らせタイトル10', 'お知らせ本文10', '2025-04-07', 1, '2025-04-07 00:00:00');

INSERT INTO `maintenance_information` (`id`, `title`, `description`, `notice_date`, `notice_start_date`, `notice_end_date`, `is_delete`, `stamp`)
    VALUES
    (1, 'メンテナンスタイトル1', 'メンテナンス本文1', '2023-04-07', '2023-04-07', '2025-04-08', 0, '2023-04-07 00:00:00'),
    (2, 'メンテナンスタイトル2', 'メンテナンス本文2', '2023-04-07', '2023-04-07', '2025-04-08', 0, '2023-04-07 00:00:00'),
    (3, 'メンテナンスタイトル3', 'メンテナンス本文3', '2023-04-07', '2023-04-07', '2025-04-08', 0, '2023-04-07 00:00:00'),
    (4, 'メンテナンスタイトル4', 'メンテナンス本文4', '2023-04-07', '2023-04-07', '2025-04-08', 0, '2023-04-07 00:00:00'),
    (5, 'メンテナンスタイトル5', 'メンテナンス本文5', '2023-04-07', '2023-04-07', '2025-04-08', 0, '2023-04-07 00:00:00'),
    (6, 'メンテナンスタイトル6', 'メンテナンス本文6', '2023-04-07', '2023-04-07', '2025-04-08', 0, '2023-04-07 00:00:00'),
    (7, 'メンテナンスタイトル7', 'メンテナンス本文7', '2023-04-07', '2023-04-07', '2025-04-08', 0, '2023-04-07 00:00:00'),
    (8, 'メンテナンスタイトル8', 'メンテナンス本文8', '2023-04-07', '2023-04-07', '2025-04-08', 0, '2023-04-07 00:00:00'),
    (9, 'メンテナンスタイトル9', 'メンテナンス本文9', '2023-04-07', '2023-04-07', '2025-04-08', 0, '2023-04-07 00:00:00'),
    (10, 'メンテナンスタイトル10', 'メンテナンス本文10', '2023-04-07', '2023-04-07', '2025-04-08', 0, '2023-04-07 00:00:00');