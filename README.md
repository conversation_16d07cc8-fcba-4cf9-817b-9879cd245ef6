# exnoapf-olympus-othrys-pf-information-api

![Lint Code Base](https://git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/workflows/Lint%20Code%20Base/badge.svg)
![Build Container](https://git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/workflows/Build%20Container/badge.svg)
![Unit Test](https://git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/workflows/Unit%20Test/badge.svg)

Developer向け告知情報ドメイン

## 導入・利用方法
各種設定を行わないと起動することができません。  
必ず以下のドキュメントを参照してください。  

- [GoのLocal開発環境準備](https://confl.arms.dmm.com/pages/viewpage.action?pageId=2181596444)

### プライベートリポジトリ
また、実行するためにはプライベートリポジトリにあるパッケージが必要なので、そのパッケージリポジトリに対してREAD権限が必要です。
権限を持たない場合このようなエラーになります。

```
Build application...
go: git.dmm.com/exnoa-platform/exnoapf-shark-go-microkit@v0.9.6: reading git.dmm.com/exnoa-platform/exnoapf-shark-go-microkit/go.mod at revision v0.9.6: unknown revision v0.9.6
```

## ローカル環境の実行手順

1. `./scripts/mysql-shell` を利用してmysqlクライアントを起動
2. `database/schema.sql`を実行
3. mysqlから抜けて、`./scripts/run`
4. http://localhost:8224/announcement/list
    - ポート番号を決定している場所は`application/infrastructure/router.go`です。
5. リストが返ってくれば成功です
6. DBから情報を取得する系
    - `application/infrastructure/router.go`でエンドポイントを確認して試してください
    - データを返すためにはDBにデータを入れる必要があります
