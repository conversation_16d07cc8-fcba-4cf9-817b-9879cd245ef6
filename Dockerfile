# builder container
FROM golang:1.24-alpine3.21 AS builder
# hadolint ignore=DL3018
RUN apk update && apk add --no-cache git

# 非特権ユーザーを作成
ARG GID=57005
ARG UID=57005
RUN addgroup\
    --gid "$GID"\
    www\
  && adduser\
    --disabled-password\
    --gecos ""\
    --ingroup www\
    --no-create-home\
    --uid "$UID"\
    www

WORKDIR /usr/src/app
ENV GOPRIVATE="git.dmm.com/exnoa-platform/*"
ARG ACCOUNT
ARG TOKEN
RUN git config --global url."https://${ACCOUNT}:${TOKEN}@git.dmm.com".insteadOf "https://git.dmm.com"
COPY go.mod .
COPY go.sum .
RUN go mod download
COPY . .
RUN go build -o app ./application

#####################
FROM alpine:3.21

ARG GID=57005
ARG UID=57005

# install SSL root certificates, curl(for healthcheck)
# hadolint ignore=DL3018
RUN apk update && apk add --no-cache ca-certificates curl tzdata && \
	update-ca-certificates

COPY --from=builder /etc/passwd /etc/passwd
COPY --from=builder /usr/src/app/app /var/app/
RUN chown -R $UID:$GID /var/app

USER $UID
WORKDIR /var/app
CMD ["/var/app/app"]
