# service name (混乱を避けるためk8sマニフェストのサービス名と同じにすること)
export SERVICE_NAME=olympus-othrys-pf-information

export APP_ENV=local
export PORT=8224
export TRACE_SAMPLE_RATE=1.0
export IS_PUBLIC_ENDPOINT=false # クラスタ上でtraceが有効化されたnginx-ingress経由でリクエストを受けていればfalse
export APP_LOG_LEVEL=debug

# use private gomodule
export GOPRIVATE=git.dmm.com/exnoa-platform/*

# Datadog
export DD_AGENT_EXIST=false
export DD_PROFILE_ENABLE=false
export DD_TRACE_PROPAGATION_STYLE_INJECT=B3
export DD_TRACE_PROPAGATION_STYLE_EXTRACT=B3

# OpenMetrics
export OPENMETRICS_ENABLE=true
export OPENMETRICS_PORT=9530

# local development envs
export WRITE_DATABASE_HOST=127.0.0.1
export WRITE_DATABASE_PORT=3439
export WRITE_DATABASE_USER=root

# freegame developer
export DB_FREEGAME_DEVELOPER_DATABASE=freegame_developer
export DB_FREEGAME_DEVELOPER_TIMEZONE=Asia/Tokyo
export DB_FREEGAME_DEVELOPER_HOST=127.0.0.1
export DB_FREEGAME_DEVELOPER_PORT=3439
export DB_FREEGAME_DEVELOPER_USERNAME=root
export DB_FREEGAME_DEVELOPER_PASSWORD=root
export DB_FREEGAME_DEVELOPER_SLAVE_HOST=127.0.0.1
export DB_FREEGAME_DEVELOPER_SLAVE_PORT=3439
export DB_FREEGAME_DEVELOPER_SLAVE_USERNAME=root
export DB_FREEGAME_DEVELOPER_SLAVE_PASSWORD=root
export DB_FREEGAME_DEVELOPER_MAX_OPEN_CONNS_NUM=1000
export DB_FREEGAME_DEVELOPER_MAX_IDLE_CONNS_NUM=1000
export DB_FREEGAME_DEVELOPER_CONN_MAX_LIFETIME_SEC=300
export DB_FREEGAME_DEVELOPER_METRICS_NAME=olympus-othrys-pf-information-freegamedeveloper-db
export RDB_TRACE_SERVICE_NAME=olympus-othrys-pf-information-rdb


export ENDPOINT_EXAMPLE=https://example.com
