package main

import (
	"fmt"
	"net/http"

	"git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/entities"
)

func main() {
	// Example 1: Create SuccessResponse with embedded Response struct explicitly
	successResponse1 := entities.SuccessResponse{
		Response: entities.Response{
			Status:  http.StatusOK,
			Message: "OK",
			Error:   map[string]interface{}{},
			Body: map[string]interface{}{
				"data": "Some data",
			},
		},
	}
	fmt.Printf("Success Response 1: %+v\n", successResponse1)

	// Example 2: Create a Response first, then create SuccessResponse
	baseResponse := entities.Response{
		Status:  http.StatusOK,
		Message: "OK",
		Error:   map[string]interface{}{},
		Body:    "Some string data",
	}
	successResponse2 := entities.SuccessResponse{
		Response: baseResponse,
	}
	fmt.Printf("Success Response 2: %+v\n", successResponse2)

	// Example 3: Create ErrorResponse with BadRequestError
	errorResponse := entities.ErrorResponse{
		Response: entities.Response{
			Status:  http.StatusBadRequest,
			Message: "Bad Request",
			Error: entities.BadRequestError{
				BaseError: entities.BaseError{
					ErrorCode: "400",
					Message:   "Invalid input parameter",
				},
				ValidationError: map[string]string{
					"field": "This field is required",
				},
			},
			Body: map[string]interface{}{},
		},
	}
	fmt.Printf("Error Response: %+v\n", errorResponse)

	// Example 4: Create not found error response
	notFoundResponse := entities.ErrorResponse{
		Response: entities.Response{
			Status:  http.StatusNotFound,
			Message: "Not Found",
			Error: entities.NotFoundError{
				BaseError: entities.BaseError{
					ErrorCode: "404",
					Message:   "Resource not found",
				},
			},
			Body: map[string]interface{}{},
		},
	}
	fmt.Printf("Not Found Response: %+v\n", notFoundResponse)
}
