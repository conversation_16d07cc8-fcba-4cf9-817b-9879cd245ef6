package examples

import (
	"fmt"

	"git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/entities"
)

func ExampleWithHelpers() {
	// Example 1: Using helper function to create SuccessResponse
	successResponse := entities.NewSuccessResponse(map[string]interface{}{
		"items": []string{"item1", "item2"},
		"count": 2,
	})
	fmt.Printf("Success Response: %+v\n", successResponse)

	// Example 2: Using helper function to create BadRequestResponse
	badRequestResponse := entities.NewBadRequestResponse(
		"Invalid input parameter",
		map[string]string{"field": "This field is required"},
	)
	fmt.Printf("Bad Request Response: %+v\n", badRequestResponse)

	// Example 3: Using helper function to create NotFoundResponse
	notFoundResponse := entities.NewNotFoundResponse("Resource not found")
	fmt.Printf("Not Found Response: %+v\n", notFoundResponse)

	// Example 4: Using helper function to create InternalServerErrorResponse
	serverErrorResponse := entities.NewInternalServerErrorResponse("Database connection failed")
	fmt.Printf("Server Error Response: %+v\n", serverErrorResponse)
}
