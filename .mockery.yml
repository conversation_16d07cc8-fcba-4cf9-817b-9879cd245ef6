all: false
dir: '{{.InterfaceDir}}'
filename: mocks/{{.InterfaceName}}.go
force-file-write: true
formatter: goimports
log-level: info
structname: '{{.InterfaceName}}'
pkgname: 'mocks'
recursive: false
require-template-schema-exists: true
template: testify
template-schema: '{{.Template}}.schema.json'
packages:
  git.dmm.com/exnoa-platform/exnoapf-olympus-othrys-pf-information-api/application/usecases:
    config:
      all: true
