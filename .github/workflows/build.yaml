name: Build Container

on:
  push:
    branches:
      - "main"
    tags:
      - v[0-9]+.[0-9]+.[0-9]+
  pull_request:

jobs:
  build-push-image:
    uses: exnoa-platform/exnoapf-olympus-github-actions-workflows-config/.github/workflows/build.yaml@main
    secrets: inherit

  # 自動でSTG環境のマニフェストを更新したい場合はコメントを外してください
  # update-manifest:
  #   # ビルドフェイズ終了後に実行
  #   needs: build-push-image
  #   uses: exnoa-platform/exnoapf-olympus-github-actions-workflows-config/.github/workflows/update-manifest.yaml@main
  #   with:
  #     MANIFEST_PATH: ${{ github.sha }}
  #     MANIFEST_REPO: dmm-com/exnoapf-shark-manifests
  #     MANIFEST_FILE: ./services/olympus/xxxxxxxxxx/values-stg.yaml
  #   secrets: inherit
