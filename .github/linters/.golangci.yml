version: "2"
output:
  formats:
    text:
      path: stdout
  path-mode: "abs"
linters:
  enable:
    - asasalint
    - asciicheck
    - bidichk
    - bodyclose
    - canonicalheader
    - containedctx
    - contextcheck
    - copyloopvar
    - cyclop
    - decorder
    - dogsled
    - dupl
    - dupword
    - durationcheck
    - err113
    - errchkjson
    - errname
    - errorlint
    - exhaustive
    - fatcontext
    - forbidigo
    - forcetypeassert
    - ginkgolinter
    - gocheckcompilerdirectives
    - gochecknoglobals
    - gochecknoinits
    - gochecksumtype
    - gocognit
    - goconst
    - gocritic
    - gocyclo
    - goheader
    - gomoddirectives
    - gomodguard
    - goprintffuncname
    - gosec
    - grouper
    - iface
    - importas
    - inamedparam
    - intrange
    - ireturn
    - loggercheck
    - maintidx
    - makezero
    - mirror
    - misspell
    - mnd
    - musttag
    - nakedret
    - nestif
    - nilerr
    - nilnesserr
    - nilnil
    - nlreturn
    - noctx
    - nolintlint
    - nosprintfhostport
    - paralleltest
    - perfsprint
    - prealloc
    - predeclared
    - promlinter
    - protogetter
    - reassign
    - recvcheck
    - revive
    - rowserrcheck
    - sloglint
    - spancheck
    - sqlclosecheck
    - staticcheck
    - testableexamples
    - testifylint
    - testpackage
    - thelper
    - tparallel
    - unconvert
    - unparam
    - usestdlibvars
    - usetesting
    - wastedassign
    - whitespace
    - wrapcheck
    - zerologlint
  disable:
    - depguard
    - exhaustruct
    - funlen
    - godot
    - godox
    - gosmopolitan
    - interfacebloat
    - lll
    - nonamedreturns
    - tagalign
    - tagliatelle
    - varnamelen
    - wsl
  settings:
    errcheck:
      check-blank: false
    gocyclo:
      min-complexity: 15
    revive:
      rules:
        - name: var-naming
          disabled: true
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    rules:
      - linters:
          - dupl
          - goconst
          - gosec
        path: _test\.go
    paths:
      - third_party$
      - builtin$
      - examples$
formatters:
  enable:
    - gci
    - gofmt
    - gofumpt
    - goimports
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
